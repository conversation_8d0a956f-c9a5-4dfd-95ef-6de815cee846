#!/usr/bin/env python3
"""
Example script showing how to convert vector store content back to text

This demonstrates that once data is vectorized, you can still retrieve 
the original text content from the vector store.
"""

import openai
import json
from config import create_app_settings
from ai_core.models.config import Config as AiCoreConfig
from ai_core.answer_questions import extract_text_from_vector_store

def demonstrate_vector_to_text():
    """
    Demonstrate how to convert vector store content back to original text
    """
    # Initialize settings
    settings, _ = create_app_settings("ai_core", AiCoreConfig)
    openai.api_key = settings.openai_key
    
    print("="*70)
    print("VECTOR STORE TO TEXT CONVERSION DEMONSTRATION")
    print("="*70)
    
    # Create a sample conversation for demonstration
    sample_conversation = {
        "conversation": [
            {
                "speaker": "Patient",
                "text": "I've been having trouble sleeping lately. I wake up multiple times during the night."
            },
            {
                "speaker": "Doctor", 
                "text": "How long has this been going on? Are you taking any medications?"
            },
            {
                "speaker": "Patient",
                "text": "About 3 weeks now. I take blood pressure medication in the morning."
            },
            {
                "speaker": "Doctor",
                "text": "I see. Let's discuss some sleep hygiene practices that might help."
            }
        ]
    }
    
    print("1. ORIGINAL TEXT CONTENT:")
    print("-" * 30)
    original_json = json.dumps(sample_conversation, indent=2)
    print(original_json)
    
    try:
        # Create a temporary file with the conversation
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            json.dump(sample_conversation, temp_file)
            temp_file_path = temp_file.name
        
        print("\n2. CREATING VECTOR STORE:")
        print("-" * 30)
        
        # Create vector store
        vector_store = openai.beta.vector_stores.create(name="demo_vector_store")
        print(f"✓ Created vector store with ID: {vector_store.id}")
        
        # Upload file to vector store
        with open(temp_file_path, "rb") as file:
            file_batch = openai.beta.vector_stores.file_batches.upload_and_poll(
                vector_store_id=vector_store.id,
                files=[file]
            )
        
        print(f"✓ Uploaded file to vector store. Status: {file_batch.status}")
        
        print("\n3. CONVERTING VECTOR STORE BACK TO TEXT:")
        print("-" * 30)
        
        # Extract text from vector store
        retrieved_text = extract_text_from_vector_store(vector_store.id)
        
        print("✓ Successfully retrieved original text from vector store:")
        print(retrieved_text)
        
        print("\n4. VERIFICATION:")
        print("-" * 30)
        
        # Parse the retrieved text and compare
        try:
            retrieved_data = json.loads(retrieved_text)
            if retrieved_data == sample_conversation:
                print("✅ SUCCESS: Retrieved text matches original content exactly!")
            else:
                print("⚠️  Retrieved text differs from original")
        except json.JSONDecodeError:
            print("⚠️  Retrieved text is not valid JSON")
        
        # Cleanup
        print("\n5. CLEANUP:")
        print("-" * 30)
        openai.beta.vector_stores.delete(vector_store.id)
        print("✓ Deleted vector store")
        
        import os
        os.unlink(temp_file_path)
        print("✓ Deleted temporary file")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
    
    print("\n" + "="*70)
    print("KEY TAKEAWAYS:")
    print("="*70)
    print("• Vector stores preserve the original text content")
    print("• You can always retrieve the original text using extract_text_from_vector_store()")
    print("• Vectorization is just for efficient similarity search")
    print("• The actual text sent to the LLM comes from the original content")
    print("="*70)

if __name__ == "__main__":
    demonstrate_vector_to_text()
