import logging
import json
import time
import uuid
import os
import mimetypes
from fastapi import Request
from queues.interface import QueueClient
from database.interface import DatabaseAdapter
from config import get_settings
from ai_core.models.config import Config as AiCoreConfig
from pathlib import Path
from fastapi.responses import FileResponse

from ai_core.models.transcription_request import TranscriptionRequest
from ai_core.answer_questions import answer_questions
from ai_core.deepgram_transcription import transform, transcribe_buffer
from ai_core.slack import send_slack_message
from ai_core.service_response import error_message, info_message, warning_message

logger = logging.getLogger(__name__)
    
VERSION_FILE = "version.json"

def load_version():
    """Load the version from version.json safely."""
    try:
        if os.path.exists(VERSION_FILE):
            with open(VERSION_FILE, "r") as f:
                data = json.load(f)
                return data
    except (json.JSONDecodeError, IOError) as e:
        logger.warning(f"Error loading version.json: {e}")
    return {"version": "0.0.0"}


# write - Create an item
def create_transcription_request(item: TranscriptionRequest, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============create_transcription_request called==============")
    settings: AiCoreConfig = get_settings()
    logger.info(f"settings: [{settings}]")

    item.started = int(time.time())

    if not db:
        raise ValueError("No database connection provided.")
    
    request_id = item.id if hasattr(item, "id") and item.id else str(uuid.uuid4())
    logger.info(f"Using request_id: {request_id}")
    new_item = item.model_dump()
    new_item["id"] = request_id  # Store UUID in the database

    logger.info(item)

    # FIXME - merge multiple audio files into one
    # should only be 1 file per request
    if len(item.audio_files) != 1:
        raise ValueError("Invalid number of files in the request.")
    
    audio_filename = item.audio_files[0]
    table = item.company_id

    logger.info(f"audio_filename: {audio_filename}")
    prefix_input = f"{item.visit_id}/{item.assessment_id}/input"
    prefix_output = f"{item.visit_id}/{item.assessment_id}/output"

    audio_buffer = None
    audio_buffer_size = None
    transcription_time = None

    if item.mode == "all-stages":
        audio_buffer = db.get_binary_item(table, f"{prefix_input}/{audio_filename}")
        if not audio_buffer:
            raise ValueError(f"Audio file not found in: {table}/{prefix_input}/{audio_filename}")
        audio_buffer_size = len(audio_buffer)

        logger.info("starting deepgram transcription")
        send_slack_message(f"Starting transcription for: {item.id} with audio file: {audio_filename} size: {audio_buffer_size}")
        deepgram_json = transcribe_buffer(audio_buffer)
        if not deepgram_json:
            raise ValueError(f"Deepgram transcription failed for file: {prefix_input}/{audio_filename}")
    
        transcription_time = int(time.time() - item.started)

        deepgram_dict = json.loads(deepgram_json)
        
        logger.info(f"saving deepgram transformation {prefix_output}/transcription.json")    
        db.insert_item(table, f"{prefix_output}/transcription.json", deepgram_dict)
        item.transcription_files.append(f"{table}/{prefix_output}/transcription.json")
        # publish timings metrics errors slack ?
        # save or push file to storage for debugging

        logger.info("deepgram transformation")
        conversation = transform(deepgram_json)
        if not conversation:
            raise ValueError(f"conversation transformation failed for {table}/{prefix_input}/{audio_filename}")
        db.insert_item(table, f"{prefix_output}/conversation.json", conversation)
        item.conversation_files.append(f"{table}/{prefix_output}/conversation.json")
    else:
        # get conversation from db
        conversation = db.get_item(table, f"{prefix_output}/conversation.json")

    logger.info("answer questions")
    question_file = f"{prefix_input}/{item.question_files[0]}"
    questions = db.get_item(table, question_file)
    if not questions:
        raise ValueError(f"Questions not found in: {table}/{question_file}")
    send_slack_message(f"Processing questions for: {item.id}")
    answers_json = answer_questions(conversation, questions, item.client_id, item.assessment_id)
    
    if not answers_json:
        raise ValueError("Answers generation failed.")

    db.insert_item(table, f"{prefix_output}/answers.json", answers_json)
    item.answer_files.append(f"{table}/{prefix_output}/answers.json")

    # generate score
    responses = answers_json["Responses"]

    total_questions = len(responses)

    item.version = load_version()

    # Ensure responses is a list of dictionaries
    if not isinstance(responses, list) or not all(isinstance(r, dict) for r in responses):
        raise ValueError("Invalid format for responses. Expected a list of dictionaries.")

    # Collect all answered questions
    item.answered_questions = [r for r in responses if r.get("answer_text") != ["Not Available"]]
    answered_questions_cnt = len(item.answered_questions)
    unanswered_questions = total_questions - answered_questions_cnt
    percentage_answered = int((answered_questions_cnt / total_questions) * 100)

    time_processing = int(time.time() - item.started)
    version = load_version()
    if not version:
        version = {"version": "0.0.0"}
    item.version = version.get("version")
    item.commit = version.get("commit")

    slack_message = (f"Score.\n\n"
    f"Date : *{time.ctime(time.time())}*\n"
    f"ID : *{item.id}*\n"
    f"User ID : *{item.user_id}*\n"
    f"Client ID : *{item.client_id}*\n"
    f"Assessment ID : *{item.assessment_id}*\n"
    f"Company ID : *{item.company_id}*\n"
    f"Visit ID : *{item.visit_id}*\n"
    f"Total Questions : *{total_questions}*\n"
    f"Answered Questions : *{answered_questions_cnt}*\n"
    f"Unanswered Questions : *{unanswered_questions}*\n"
    f"Time Processing : *{time_processing} seconds*\n"
    f"Percentage Answered : *{percentage_answered}%*\n"
    f"Input Files : *{prefix_input}*\n"
    f"Audio File Size : *{audio_buffer_size} bytes*\n"
    f"Version : *{version}*\n"
    )

    item.total_questions = total_questions
    item.answered_questions_cnt = answered_questions_cnt
    item.total_time = time_processing
    item.transcription_time = transcription_time
    item.transcription_service_type = "deepgram"
    item.transcription_service_model = settings.deepgram_model
    item.ai_service_type = "openai"
    item.ai_service_model = settings.openai_model
    item.ai_temperature = settings.ai_temperature
    # request.ai_prompt = ai_prompt Currently too complicated because of user input, could be INSTRUCTIONS
    item.ai_embeddings = ["conversation.json"]
    item.audio_buffer_size = audio_buffer_size

    item.completed = int(time.time())
    item.status = "completed"

    result = item.model_dump()
    
    db.insert_item(table, f"{prefix_output}/message_out.json", result)
    db.insert_item(table, f"{prefix_output}/slack_message.json", slack_message)


    db.insert_item("results", f"{item.id}.json", result)

    all_results = db.get_item("results", "all-results.json")
    if not all_results:
        all_results = []
    all_results.append(result)
    db.insert_item("results", "all-results.json",  all_results)


    send_slack_message(slack_message)
    logger.info(f"=== ai core processing finished {item.id} {prefix_output}/answers.json ===")
    return item

# read - get all items
def get_all_transcription_request(db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_all_transcription_request called==============")
    return db.get_item("results", "all-results.json")

# read - get an item
def get_transcription_request(id: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_transcription_request called==============")
    logger.info(f"Received request to retrieve transcription_request with id: {id}")
    item = db.get_item("transcription_request", id)
    return item

# write - update an item (without modifying ID)
def update_transcription_request(id: str, new_item: TranscriptionRequest, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============update_transcription_request called==============")
    logger.info(new_item)
    db.update_item("transcription_request", id, new_item.model_dump())
    return db.get_item("transcription_request", id)

# write - delete an item
def delete_transcription_request(id: str, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============delete_transcription_request called==============")
    logger.info(f"Received request to delete transcription_request with id {id}")
    item = db.get_item("transcription_request", id)
    if not item:
        logger.warning(f"TranscriptionRequest with id {id} not found")
        return None
    db.delete_item("transcription_request", id)
    return item
