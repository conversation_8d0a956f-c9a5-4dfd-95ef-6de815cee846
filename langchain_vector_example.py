#!/usr/bin/env python3
"""
Example showing how <PERSON><PERSON><PERSON><PERSON> would give you visibility into vector retrieval
"""

import json
from langchain.vectorstores import Chroma
from langchain.embeddings import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from langchain.chains import RetrievalQA
from langchain.llms import OpenAI

def demonstrate_langchain_vector_retrieval():
    """
    Show how <PERSON><PERSON><PERSON><PERSON> exposes the actual retrieved content
    """
    
    # Sample conversation data
    conversation_data = [
        {
            "speaker_id": 0,
            "text": "I'm gonna say three words for you to remember. Please repeat the words after I have said all three. Okay? Yes. Alright."
        },
        {
            "speaker_id": 0,
            "text": "So the three words are sock, blue, and bed. Now tell me the three words. Sock, bed, and blue. Very good."
        },
        {
            "speaker_id": 0,
            "text": "Let's go back to an earlier question. What were those three words that I asked you, to repeat? Sock. And I don't remember the other two. Okay. Very good."
        },
        {
            "speaker_id": 0,
            "text": "One, was a piece of furniture. Oh, a bed. Bed. Right? And the other was a color."
        }
    ]
    
    print("="*70)
    print("LANGCHAIN VECTOR RETRIEVAL DEMONSTRATION")
    print("="*70)
    
    # Convert to documents
    documents = []
    for i, item in enumerate(conversation_data):
        doc = Document(
            page_content=item["text"],
            metadata={"speaker_id": item["speaker_id"], "chunk_id": i}
        )
        documents.append(doc)
    
    # Create embeddings and vector store
    embeddings = OpenAIEmbeddings()
    vectorstore = Chroma.from_documents(documents, embeddings)
    
    # Create retriever
    retriever = vectorstore.as_retriever(search_kwargs={"k": 5})
    
    # Test query
    query = "What were the three words the patient was asked to remember?"
    
    print(f"\n1. QUERY: {query}")
    print("-" * 50)
    
    # THIS IS WHAT LANGCHAIN LETS YOU SEE:
    retrieved_docs = retriever.get_relevant_documents(query)
    
    print(f"\n2. RETRIEVED CHUNKS FROM VECTOR DATABASE:")
    print("-" * 50)
    
    for i, doc in enumerate(retrieved_docs):
        print(f"\nChunk {i+1}:")
        print(f"  Content: {doc.page_content}")
        print(f"  Metadata: {doc.metadata}")
        # You can also get similarity scores with some retrievers
        
    print(f"\n3. WHAT GETS SENT TO LLM:")
    print("-" * 50)
    context = "\n\n".join([doc.page_content for doc in retrieved_docs])
    print(f"Combined context:\n{context}")
    
    # Now create the QA chain
    qa_chain = RetrievalQA.from_chain_type(
        llm=OpenAI(temperature=0),
        chain_type="stuff",
        retriever=retriever,
        return_source_documents=True  # This returns the source docs!
    )
    
    result = qa_chain({"query": query})
    
    print(f"\n4. LLM RESPONSE:")
    print("-" * 50)
    print(f"Answer: {result['result']}")
    
    print(f"\n5. SOURCE DOCUMENTS USED:")
    print("-" * 50)
    for i, doc in enumerate(result['source_documents']):
        print(f"Source {i+1}: {doc.page_content}")
    
    return retrieved_docs, result

# Custom retriever that shows similarity scores
class VerboseRetriever:
    def __init__(self, vectorstore):
        self.vectorstore = vectorstore
    
    def get_relevant_documents_with_scores(self, query, k=5):
        """Get documents with similarity scores"""
        results = self.vectorstore.similarity_search_with_score(query, k=k)
        
        print(f"\nDETAILED VECTOR SEARCH RESULTS:")
        print("-" * 40)
        
        for i, (doc, score) in enumerate(results):
            print(f"Result {i+1}:")
            print(f"  Similarity Score: {score:.4f}")
            print(f"  Content: {doc.page_content}")
            print(f"  Metadata: {doc.metadata}")
            print()
        
        return [doc for doc, score in results]

def demonstrate_with_scores():
    """Show retrieval with similarity scores"""
    
    # Sample data
    conversation_data = [
        {"speaker_id": 0, "text": "So the three words are sock, blue, and bed. Now tell me the three words. Sock, bed, and blue."},
        {"speaker_id": 0, "text": "What were those three words? Sock. And I don't remember the other two."},
        {"speaker_id": 0, "text": "One, was a piece of furniture. Oh, a bed. Bed. Right? And the other was a color."}
    ]
    
    documents = [Document(page_content=item["text"], metadata={"speaker_id": item["speaker_id"]}) 
                for item in conversation_data]
    
    embeddings = OpenAIEmbeddings()
    vectorstore = Chroma.from_documents(documents, embeddings)
    
    retriever = VerboseRetriever(vectorstore)
    
    query = "What three words was the patient asked to remember?"
    print(f"Query: {query}")
    
    # This will show you exactly what's retrieved with scores
    docs = retriever.get_relevant_documents_with_scores(query)
    
    return docs

if __name__ == "__main__":
    print("LangChain gives you full visibility into vector retrieval!")
    print("You can see:")
    print("- Actual retrieved text chunks")
    print("- Similarity scores") 
    print("- Source metadata")
    print("- What exactly gets sent to the LLM")
    print("\nUnlike OpenAI's black box approach.")
